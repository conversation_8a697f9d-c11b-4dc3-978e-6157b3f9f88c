# 外设配置和文件名问题修复总结

## 问题描述

用户报告了两个主要问题：

1. **外设配置问题**：AI分析需求时无法正常填入引脚配置，自动添加的外设无法编辑
2. **中文文件名问题**：系统生成了名为"有源蜂鸣器.cpp"的中文文件名，导致编译和跨平台兼容性问题

## 具体问题分析

### 1. 外设配置问题

**问题现象**：
- 用户输入："使用HC-SR04超声波传感器trig接23号引脚，echo接21号引脚"
- AI生成的外设类型为`distance-sensing`，前端无法识别
- 引脚配置格式不匹配：AI生成`"pin": 23`，但前端期望`"pins": [...]`格式

**根本原因**：
- AI prompt中的引脚配置格式过时
- 前端外设数据库中的功能类型映射不完整
- 缺少AI生成数据的标准化处理

### 2. 中文文件名问题

**问题现象**：
- 系统生成了"有源蜂鸣器.cpp"等中文文件名
- 导致编译错误和跨平台兼容性问题

**根本原因**：
- AI模块架构师根据中文外设名称生成中文task_id
- 缺少文件名规范化机制

## 实施的修复方案

### 1. 外设配置修复

#### 1.1 更新AI分析prompt
**文件**: `app/services/project_analyzer_service.py`

**修改内容**：
- 将引脚配置从单一`pin`字段改为`pins`数组格式
- 添加具体的引脚配置示例：
  ```json
  "pins": [{"name": "TRIG_PIN", "number": 23}, {"name": "ECHO_PIN", "number": 21}]
  ```
- 添加关键词识别规则，特别针对HC-SR04和蜂鸣器
- 更新示例，使用正确的引脚格式

#### 1.2 前端数据标准化
**文件**: `app/templates/index.html`

**新增功能**：
- `standardizePeripheralData()` 函数：自动转换AI返回的数据格式
- 功能类型映射：`distance-sensing` → `DISTANCE_MEASUREMENT`
- 引脚格式转换：`pin: 23` → `pins: [{"name": "PIN", "number": 23}]`
- 智能引脚名称推断：根据外设型号自动设置正确的引脚名称

#### 1.3 外设编辑功能增强
**修改内容**：
- 在`editPeripheral()`函数中添加功能类型映射
- 支持旧格式数据的自动转换
- 改善错误处理和用户提示

### 2. 文件名规范化修复

#### 2.1 添加文件名规范化函数
**文件**: `app/langgraph_def/graph_builder.py`

**新增功能**：
```python
def sanitize_filename(name: str) -> str:
    """规范化文件名，将中文和特殊字符转换为英文"""
```

**映射规则**：
- `有源蜂鸣器` → `active_buzzer`
- `HC-SR04` → `hcsr04`
- `超声波传感器` → `ultrasonic_sensor`
- `蜂鸣器` → `buzzer`

#### 2.2 更新AI prompt
**修改内容**：
- 在模块架构师prompt中添加文件命名规则
- 强调所有task_id必须使用英文字符
- 提供具体的命名示例

#### 2.3 应用文件名规范化
**修改位置**：
- 文件生成逻辑：自动对所有task_id应用规范化
- 开发者节点：在生成代码时使用规范化的文件名
- #include语句：确保头文件引用使用正确的文件名

## 测试验证

### 1. 外设配置测试
创建了`test_peripheral_fix.html`来验证：
- AI数据格式转换
- 功能类型映射
- 引脚配置标准化

### 2. 文件名规范化测试
创建了`test_filename_sanitization.py`来验证：
- 中文名称转换
- 特殊字符处理
- 边界情况处理

**测试结果**：13/14个测试用例通过，1个小问题已修复。

## 预期效果

### 1. 外设配置改善
- AI能正确识别HC-SR04的双引脚配置
- 自动添加的外设可以正常编辑
- 支持更多外设类型的自动配置

### 2. 文件名问题解决
- 所有生成的文件名都使用英文字符
- 避免编译错误和跨平台问题
- 提高代码的可维护性

## 向后兼容性

所有修改都保持向后兼容：
- 旧格式的外设配置会自动转换为新格式
- 现有的英文文件名不受影响
- 不会破坏现有的工作流程

## 建议的后续改进

1. **扩展外设支持**：添加更多传感器的自动配置规则
2. **改进AI训练**：使用更多示例训练AI正确识别引脚配置
3. **用户界面优化**：提供更直观的外设配置编辑界面
4. **错误处理增强**：提供更详细的错误信息和修复建议
