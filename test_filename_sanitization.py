#!/usr/bin/env python3
"""
测试文件名规范化功能
"""

def sanitize_filename(name: str) -> str:
    """
    规范化文件名，将中文和特殊字符转换为英文，确保文件名兼容性。
    
    Args:
        name: 原始名称（可能包含中文）
        
    Returns:
        str: 规范化后的英文文件名
    """
    # 中文到英文的映射表
    chinese_to_english = {
        '有源蜂鸣器': 'active_buzzer',
        '蜂鸣器': 'buzzer',
        '超声波传感器': 'ultrasonic_sensor',
        '距离传感器': 'distance_sensor',
        '光照传感器': 'light_sensor',
        '温湿度传感器': 'temp_humidity_sensor',
        '温度传感器': 'temperature_sensor',
        '人体红外传感器': 'pir_sensor',
        '运动传感器': 'motion_sensor',
        'LED灯': 'led',
        '继电器': 'relay',
        '显示屏': 'display',
        '驱动器': 'driver',
        '传感器': 'sensor'
    }
    
    # 首先尝试直接映射
    if name in chinese_to_english:
        return chinese_to_english[name]
    
    # 如果是已知的型号，直接处理
    if name.upper() in ['HC-SR04', 'HC-SR501', 'BH1750', 'DHT11', 'DHT22', 'DS18B20']:
        return name.lower().replace('-', '').replace(' ', '_')
    
    # 对于包含中文的复合名称，尝试部分替换
    result = name
    for chinese, english in chinese_to_english.items():
        result = result.replace(chinese, english)

    # 移除或替换特殊字符
    result = result.replace('-', '').replace(' ', '_').replace('(', '').replace(')', '')

    # 如果仍然包含中文字符，使用拼音或通用名称
    if any('\u4e00' <= char <= '\u9fff' for char in result):
        # 包含中文字符，使用通用名称
        if '传感器' in name or 'sensor' in name.lower():
            result = 'generic_sensor'
        elif '蜂鸣器' in name or 'buzzer' in name.lower():
            result = 'buzzer'
        elif 'LED' in name or 'led' in name.lower():
            result = 'led'
        else:
            result = 'generic_device'

    # 特殊处理：如果结果包含已知的传感器类型但还有其他中文，进行二次处理
    if '模块' in result:
        result = result.replace('模块', '')
        if not result or any('\u4e00' <= char <= '\u9fff' for char in result):
            if 'temp_humidity_sensor' in result:
                result = 'temp_humidity_sensor'
            else:
                result = 'generic_sensor'
    
    # 确保结果只包含字母、数字和下划线
    result = ''.join(c for c in result if c.isalnum() or c == '_').lower()
    
    return result if result else 'generic_device'

def test_sanitize_filename():
    """测试文件名规范化功能"""
    test_cases = [
        # 中文名称测试
        ('有源蜂鸣器', 'active_buzzer'),
        ('蜂鸣器', 'buzzer'),
        ('超声波传感器', 'ultrasonic_sensor'),
        ('HC-SR04', 'hcsr04'),
        ('HC-SR501', 'hcsr501'),
        ('BH1750', 'bh1750'),
        
        # 复合名称测试
        ('有源蜂鸣器_driver', 'active_buzzer_driver'),
        ('HC-SR04_driver', 'hcsr04_driver'),
        ('温湿度传感器模块', 'temp_humidity_sensor'),
        
        # 特殊字符测试
        ('LED-控制器', 'led'),
        ('继电器(5V)', 'relay5v'),
        
        # 边界情况测试
        ('', 'generic_device'),
        ('123', '123'),
        ('test_sensor', 'test_sensor'),
    ]
    
    print("文件名规范化测试结果:")
    print("=" * 50)
    
    all_passed = True
    for input_name, expected in test_cases:
        result = sanitize_filename(input_name)
        status = "✓" if result == expected else "✗"
        print(f"{status} '{input_name}' -> '{result}' (期望: '{expected}')")
        if result != expected:
            all_passed = False
    
    print("=" * 50)
    print(f"测试结果: {'全部通过' if all_passed else '有失败项'}")
    
    # 额外测试：确保结果不包含中文字符
    print("\n中文字符检查:")
    chinese_names = ['有源蜂鸣器', '超声波传感器', '温湿度传感器']
    for name in chinese_names:
        result = sanitize_filename(name)
        has_chinese = any('\u4e00' <= char <= '\u9fff' for char in result)
        status = "✗" if has_chinese else "✓"
        print(f"{status} '{name}' -> '{result}' (包含中文: {has_chinese})")

if __name__ == "__main__":
    test_sanitize_filename()
