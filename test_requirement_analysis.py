#!/usr/bin/env python3
"""
测试需求分析是否生成更详细的工程化描述
"""

import os
os.environ['LANGCHAIN_API_KEY'] = 'test-key'

def test_requirement_analysis():
    """测试需求分析的prompt生成"""
    
    # 模拟用户设备数据
    class MockDevice:
        def __init__(self, nickname, internal_device_id, board_model):
            self.nickname = nickname
            self.internal_device_id = internal_device_id
            self.board_model = board_model
    
    # 测试数据
    raw_text = "用大绿板开发一个智能报警器，使用HC-SR04超声波传感器trig接23号引脚，echo接21号引脚测定距离，如果测定距离小于一米，就调用5号引脚的低电平触发蜂鸣器，并发送测定距离到涂鸦云。"
    user_devices = [
        MockDevice("大绿板", "device-001", "ESP32-S3")
    ]
    
    try:
        # 导入并测试
        from app.services.project_analyzer_service import get_analyzer_prompt
        
        # 尝试生成prompt
        prompt = get_analyzer_prompt(raw_text, user_devices)
        print("✅ Prompt生成成功！")
        print("=" * 80)
        
        # 检查关键改进内容
        key_checks = [
            ("包含工程化详细描述要求", "工程化详细描述" in prompt),
            ("包含业务逻辑要求", "业务逻辑和条件判断" in prompt),
            ("包含阈值和触发条件", "阈值、触发条件" in prompt),
            ("包含执行器控制要求", "执行器控制的具体动作" in prompt),
            ("包含数据处理要求", "数据处理、决策逻辑" in prompt),
            ("包含错误处理要求", "错误处理和边界情况" in prompt),
            ("包含详细示例", "每5秒使用HC-SR04测量距离" in prompt),
            ("包含条件判断示例", "当测量值小于100cm时立即激活蜂鸣器" in prompt),
        ]
        
        print("关键改进内容检查:")
        all_passed = True
        for check_name, result in key_checks:
            status = "✅" if result else "❌"
            print(f"{status} {check_name}: {'通过' if result else '失败'}")
            if not result:
                all_passed = False
        
        if all_passed:
            print("\n🎉 所有检查都通过！AI应该能生成更详细的工程化描述了。")
        else:
            print("\n❌ 还有问题需要修复。")
            
        # 显示prompt中的关键改进部分
        print("\n" + "=" * 80)
        print("Prompt中的工程化描述要求:")
        lines = prompt.split('\n')
        in_description_section = False
        for i, line in enumerate(lines):
            if '工程化详细描述' in line:
                in_description_section = True
            if in_description_section:
                print(f"第{i+1}行: {line}")
                if line.strip().endswith('。"') and '例如：' in line:
                    break
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("测试需求分析Prompt改进...")
    success = test_requirement_analysis()
    print(f"\n测试结果: {'成功' if success else '失败'}")
    
    if success:
        print("\n📋 预期效果:")
        print("- AI分析将生成包含具体业务逻辑的设备描述")
        print("- 设备描述将包含阈值、条件判断、执行器控制等细节")
        print("- 开发者节点将基于详细描述生成完整的功能代码")
        print("- 生成的代码将包含距离判断和蜂鸣器控制逻辑")
