#!/usr/bin/env python3
"""
测试AI分析功能是否正确识别HC-SR04
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_prompt_generation():
    """测试prompt生成是否正确"""
    
    # 模拟用户设备数据
    class MockDevice:
        def __init__(self, nickname, internal_device_id, board_model):
            self.nickname = nickname
            self.internal_device_id = internal_device_id
            self.board_model = board_model
    
    # 测试数据
    raw_text = "用大绿板开发一个智能报警器，使用HC-SR04超声波传感器trig接23号引脚，echo接21号引脚测定距离，如果测定距离小于一米，就调用5号引脚的低电平触发蜂鸣器，并发送测定距离到涂鸦云。"
    user_devices = [
        MockDevice("大绿板", "device-001", "ESP32-S3")
    ]
    
    try:
        # 导入并测试
        from app.services.project_analyzer_service import get_analyzer_prompt
        
        # 尝试生成prompt
        prompt = get_analyzer_prompt(raw_text, user_devices)
        print("✅ Prompt生成成功！")
        print("=" * 50)
        
        # 检查关键内容是否存在
        key_checks = [
            ("HC-SR04配置示例", "TRIG_PIN" in prompt and "ECHO_PIN" in prompt),
            ("蜂鸣器配置示例", "CONTROL_PIN" in prompt),
            ("用户输入内容", raw_text in prompt),
            ("设备信息", "大绿板" in prompt),
            ("JSON格式正确", '{"name": "TRIG_PIN"' in prompt),  # 检查JSON格式
        ]
        
        print("关键内容检查:")
        all_passed = True
        for check_name, result in key_checks:
            status = "✅" if result else "❌"
            print(f"{status} {check_name}: {'通过' if result else '失败'}")
            if not result:
                all_passed = False
        
        if all_passed:
            print("\n🎉 所有检查都通过！AI应该能正确识别HC-SR04了。")
        else:
            print("\n❌ 还有问题需要修复。")
            
        # 显示prompt的关键部分
        print("\n" + "=" * 50)
        print("Prompt中的HC-SR04示例:")
        lines = prompt.split('\n')
        for i, line in enumerate(lines):
            if 'HC-SR04' in line or 'TRIG_PIN' in line or 'ECHO_PIN' in line:
                print(f"第{i+1}行: {line}")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("测试AI分析Prompt生成...")
    success = test_prompt_generation()
    print(f"\n测试结果: {'成功' if success else '失败'}")
