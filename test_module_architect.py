#!/usr/bin/env python3
"""
测试模块架构师的输入和输出
"""

import os
os.environ['LANGCHAIN_API_KEY'] = 'test-key'

def test_module_architect_prompt():
    """测试模块架构师的prompt生成"""
    
    # 模拟设备任务数据（从数据库查询结果）
    device_task = {
        'internal_device_id': '65b275d4-dbe1-491b-bab7-1de2ab72f29d',
        'board': 'ESP32-S3',
        'device_role': '智能报警器',
        'description': '使用HC-SR04测量距离，当距离小于1米时触发蜂鸣器报警，并发送数据到涂鸦云。',
        'peripherals': [
            {
                'name': '超声波传感器',
                'model': 'HC-SR04',
                'pins': [
                    {'name': 'TRIG_PIN', 'number': 23},
                    {'name': 'ECHO_PIN', 'number': 21}
                ],
                'function': 'DISTANCE_MEASUREMENT',
                'interface': 'DIGITAL',
                'implementationType': 'SPECIFIC_MODEL'
            },
            {
                'name': '蜂鸣器',
                'model': '有源蜂鸣器',
                'pins': [
                    {'name': 'CONTROL_PIN', 'number': 5}
                ],
                'function': 'BUZZER',
                'interface': 'DIGITAL',
                'implementationType': 'GENERIC_COMPONENT'
            }
        ]
    }
    
    # 生成外设信息字符串（模拟模块架构师的逻辑）
    peripherals_info_parts = []
    for p in device_task.get('peripherals', []):
        model_str = f" (Model: {p.get('model', 'N/A')})"
        pins_str = ""
        pins = p.get('pins', [])
        
        if pins:
            pin_details = ", ".join([f"{pin.get('name', 'PIN').upper()}: {pin.get('number', 'Not specified')}" for pin in pins])
            pins_str = f" (Pins: {pin_details})"
        
        peripherals_info_parts.append(f"- {p['name']}{model_str}{pins_str}")
    
    peripherals_info = "\n".join(peripherals_info_parts)
    
    print("=== 模块架构师输入数据 ===")
    print(f"设备ID: {device_task['internal_device_id']}")
    print(f"设备角色: {device_task['device_role']}")
    print(f"设备描述: {device_task['description']}")
    print("\n外设信息:")
    print(peripherals_info)
    
    # 检查外设信息是否正确
    print("\n=== 外设信息验证 ===")
    checks = [
        ("包含HC-SR04", "HC-SR04" in peripherals_info),
        ("包含TRIG_PIN", "TRIG_PIN" in peripherals_info),
        ("包含ECHO_PIN", "ECHO_PIN" in peripherals_info),
        ("包含蜂鸣器", "蜂鸣器" in peripherals_info),
        ("包含CONTROL_PIN", "CONTROL_PIN" in peripherals_info),
        ("引脚23", "23" in peripherals_info),
        ("引脚21", "21" in peripherals_info),
        ("引脚5", "5" in peripherals_info),
    ]
    
    all_passed = True
    for check_name, result in checks:
        status = "✅" if result else "❌"
        print(f"{status} {check_name}: {'通过' if result else '失败'}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 外设信息格式正确！模块架构师应该能识别所有外设。")
    else:
        print("\n❌ 外设信息有问题，需要修复。")
    
    return all_passed

if __name__ == "__main__":
    print("测试模块架构师输入数据...")
    success = test_module_architect_prompt()
    print(f"\n测试结果: {'成功' if success else '失败'}")
