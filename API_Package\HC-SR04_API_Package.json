{"HC-SR04_Interface": {"functions": [{"name": "hcsr04_init", "description": "Initializes the HC-SR04 sensor with the specified trigger and echo pins.", "return_type": "int", "parameters": [{"name": "trigger_pin", "type": "int"}, {"name": "echo_pin", "type": "int"}]}, {"name": "hcsr04_read_distance_cm", "description": "Returns the measured distance in centimeters. CRITICAL: Must send 10μs trigger pulse before measuring echo. Returns NAN on timeout/failure.", "return_type": "float", "parameters": []}, {"name": "hcsr04_read_distance_mm", "description": "Returns the measured distance in millimeters.", "return_type": "float", "parameters": []}, {"name": "hcsr04_set_timeout_us", "description": "Sets the echo pulse timeout in microseconds to avoid infinite waits.", "return_type": "void", "parameters": [{"name": "timeout_us", "type": "uint32_t"}]}]}}