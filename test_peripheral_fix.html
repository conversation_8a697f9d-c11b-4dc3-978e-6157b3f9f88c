<!DOCTYPE html>
<html>
<head>
    <title>测试外设配置修复</title>
</head>
<body>
    <h1>测试外设配置修复</h1>
    
    <script>
        // 模拟AI返回的外设数据（旧格式）
        const aiGeneratedPeripheral = {
            name: "超声波传感器",
            model: "HC-SR04",
            pin: 23, // 旧格式：单一引脚
            function: "distance-sensing", // 旧格式：功能类型
            interface: "DIGITAL",
            implementationType: "SPECIFIC_MODEL"
        };

        // 标准化AI返回的外设数据格式
        function standardizePeripheralData(peripheral) {
            const standardized = { ...peripheral };
            
            // 处理功能类型映射
            const functionTypeMapping = {
                'distance-sensing': 'DISTANCE_MEASUREMENT',
                'distance_sensing': 'DISTANCE_MEASUREMENT',
                'light-sensing': 'LIGHT_SENSING',
                'light_sensing': 'LIGHT_SENSING',
                'temp-humidity-sensing': 'TEMP_HUMIDITY_SENSING',
                'temp_humidity_sensing': 'TEMP_HUMIDITY_SENSING',
                'motion-detection': 'MOTION_DETECTION',
                'motion_detection': 'MOTION_DETECTION',
                'buzzer': 'BUZZER',
                'led-control': 'LED_CONTROL',
                'led_control': 'LED_CONTROL',
                'relay-switch': 'RELAY_SWITCH',
                'relay_switch': 'RELAY_SWITCH'
            };

            if (functionTypeMapping[standardized.function]) {
                standardized.function = functionTypeMapping[standardized.function];
            }

            // 处理引脚格式转换：从旧格式 pin 转换为新格式 pins
            if (standardized.pin !== undefined && !standardized.pins) {
                if (standardized.pin === "USER_INPUT_REQUIRED") {
                    standardized.pins = [{ name: "PIN", number: "USER_INPUT_REQUIRED" }];
                } else {
                    standardized.pins = [{ name: "PIN", number: standardized.pin }];
                }
                delete standardized.pin; // 删除旧格式
            }

            // 确保pins是数组格式
            if (!standardized.pins || !Array.isArray(standardized.pins)) {
                standardized.pins = [{ name: "PIN", number: "USER_INPUT_REQUIRED" }];
            }

            // 根据外设型号和功能类型，智能推断引脚名称
            if (standardized.model === "HC-SR04" || standardized.function === "DISTANCE_MEASUREMENT") {
                if (standardized.pins.length === 2) {
                    standardized.pins[0].name = "TRIG_PIN";
                    standardized.pins[1].name = "ECHO_PIN";
                } else if (standardized.pins.length === 1 && standardized.pins[0].name === "PIN") {
                    // 如果只有一个引脚，扩展为两个引脚
                    standardized.pins = [
                        { name: "TRIG_PIN", number: standardized.pins[0].number },
                        { name: "ECHO_PIN", number: "USER_INPUT_REQUIRED" }
                    ];
                }
            } else if (standardized.function === "BUZZER") {
                if (standardized.pins.length === 1 && standardized.pins[0].name === "PIN") {
                    standardized.pins[0].name = "CONTROL_PIN";
                }
            }

            return standardized;
        }

        // 测试转换
        console.log("原始AI数据:", aiGeneratedPeripheral);
        const standardized = standardizePeripheralData(aiGeneratedPeripheral);
        console.log("标准化后数据:", standardized);

        // 显示结果
        document.body.innerHTML += `
            <h2>测试结果</h2>
            <h3>原始AI数据:</h3>
            <pre>${JSON.stringify(aiGeneratedPeripheral, null, 2)}</pre>
            
            <h3>标准化后数据:</h3>
            <pre>${JSON.stringify(standardized, null, 2)}</pre>
            
            <h3>验证结果:</h3>
            <ul>
                <li>功能类型转换: ${aiGeneratedPeripheral.function} → ${standardized.function} ✓</li>
                <li>引脚格式转换: pin: ${aiGeneratedPeripheral.pin} → pins: ${JSON.stringify(standardized.pins)} ✓</li>
                <li>HC-SR04引脚名称: ${standardized.pins.map(p => p.name).join(', ')} ✓</li>
            </ul>
        `;
    </script>
</body>
</html>
